#!/usr/bin/env python3
"""
日志分析报告生成器
基于log_analysis_results.json生成完整的分析报告
"""

import json
import statistics
from datetime import datetime
from collections import Counter, defaultdict

def load_analysis_results(file_path='log_analysis_results.json'):
    """加载分析结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def generate_executive_summary(results):
    """生成执行摘要"""
    performance = results['performance']
    tool_usage = results['tool_usage']
    errors_warnings = results['errors_warnings']
    
    summary = f"""
# 日志分析报告 - 执行摘要

## 📊 总体概况
- **总请求数**: {performance['total_requests']} 个
- **平均响应时间**: {performance['avg_response_time']:.2f} 秒
- **工具调用总数**: {tool_usage['total_tool_calls']} 次
- **错误总数**: {errors_warnings['total_errors']} 个
- **警告总数**: {errors_warnings['total_warnings']} 个

## 🎯 关键发现
1. **性能表现**: 平均响应时间为 {performance['avg_response_time']:.2f} 秒，最快 {performance['min_response_time']:.2f} 秒，最慢 {performance['max_response_time']:.2f} 秒
2. **工具使用**: 主要使用 inverted_index ({tool_usage['tool_distribution'].get('inverted_index', 0)} 次) 和 term_sparse ({tool_usage['tool_distribution'].get('term_sparse', 0)} 次)
3. **稳定性**: 错误率为 {(errors_warnings['total_errors'] / performance['total_requests'] * 100):.1f}%
"""
    return summary

def analyze_performance_details(results):
    """分析性能详情"""
    performance = results['performance']
    response_times = performance['response_times']
    
    # 计算性能分布
    fast_requests = sum(1 for t in response_times if t < 30)
    medium_requests = sum(1 for t in response_times if 30 <= t < 60)
    slow_requests = sum(1 for t in response_times if 60 <= t < 120)
    very_slow_requests = sum(1 for t in response_times if t >= 120)
    
    total = len(response_times)
    
    analysis = f"""
## ⏱️ 性能分析详情

### 响应时间统计
- **平均响应时间**: {performance['avg_response_time']:.2f} 秒
- **中位数响应时间**: {performance['median_response_time']:.2f} 秒
- **最快响应时间**: {performance['min_response_time']:.2f} 秒
- **最慢响应时间**: {performance['max_response_time']:.2f} 秒
- **标准差**: {statistics.stdev(response_times):.2f} 秒

### 响应时间分布
- **快速 (< 30秒)**: {fast_requests} 个请求 ({fast_requests/total*100:.1f}%)
- **中等 (30-60秒)**: {medium_requests} 个请求 ({medium_requests/total*100:.1f}%)
- **较慢 (60-120秒)**: {slow_requests} 个请求 ({slow_requests/total*100:.1f}%)
- **很慢 (≥ 120秒)**: {very_slow_requests} 个请求 ({very_slow_requests/total*100:.1f}%)

### 项目指标分析
"""
    
    # 分析项目指标
    for project, metrics in performance['project_metrics'].items():
        analysis += f"\n#### {project} 项目\n"
        for metric, value in metrics.items():
            analysis += f"- **{metric}**: {value:.4f}\n"
    
    return analysis

def analyze_tool_usage_details(results):
    """分析工具使用详情"""
    tool_usage = results['tool_usage']
    
    analysis = f"""
## 🛠️ 工具使用分析

### 工具调用分布
"""
    
    total_calls = tool_usage['total_tool_calls']
    for tool, count in tool_usage['tool_distribution'].items():
        percentage = count / total_calls * 100
        analysis += f"- **{tool}**: {count} 次 ({percentage:.1f}%)\n"
    
    analysis += f"""
### 工具使用模式分析
- **总调用次数**: {total_calls}
- **主要工具**: inverted_index (倒排索引搜索)
- **辅助工具**: term_sparse (稀疏词项搜索), grep (正则表达式搜索)

### 工具效率分析
- **inverted_index**: 主要用于语义搜索，调用频率最高
- **term_sparse**: 用于文档类型特定搜索，效果良好
- **grep**: 用于精确模式匹配，成功率较低
"""
    
    return analysis

def analyze_errors_and_issues(results):
    """分析错误和问题"""
    errors_warnings = results['errors_warnings']
    
    analysis = f"""
## ⚠️ 错误和问题分析

### 错误统计
- **总错误数**: {errors_warnings['total_errors']}
- **总警告数**: {errors_warnings['total_warnings']}

### 主要错误类型
"""
    
    # 统计错误类型
    error_types = Counter()
    for error in errors_warnings['errors']:
        error_types[error.get('type', 'unknown')] += 1
    
    for error_type, count in error_types.items():
        analysis += f"- **{error_type}**: {count} 次\n"
    
    analysis += """
### 错误原因分析
1. **LLM调用失败**: 主要是502错误，可能是服务器负载过高或网络问题
2. **连接重置**: 网络连接不稳定导致的连接中断
3. **重试机制**: 系统具备自动重试功能，但仍有部分请求失败

### 警告分析
- 主要是连接重试和超时警告
- 表明网络环境不够稳定
- 需要优化连接池和重试策略
"""
    
    return analysis

def generate_recommendations(results):
    """生成改进建议"""
    performance = results['performance']
    tool_usage = results['tool_usage']
    errors_warnings = results['errors_warnings']
    
    recommendations = """
## 🚀 改进建议

### 性能优化建议

#### 1. 响应时间优化
- **当前平均响应时间**: {:.2f} 秒，建议优化到 < 60 秒
- **并发处理**: 考虑增加并发处理能力，减少排队等待时间
- **缓存机制**: 对常见查询结果进行缓存，减少重复计算

#### 2. 工具使用优化
- **inverted_index 优化**: 作为主要工具，可以优化索引结构和搜索算法
- **term_sparse 调优**: 提高稀疏搜索的准确性和效率
- **grep 工具改进**: 优化正则表达式匹配，提高成功率

### 稳定性改进建议

#### 1. 错误处理优化
- **LLM服务稳定性**: 
  - 增加负载均衡和故障转移机制
  - 实现更智能的重试策略
  - 考虑使用多个LLM服务提供商
  
- **网络连接优化**:
  - 增加连接池大小
  - 实现指数退避重试策略
  - 添加连接健康检查

#### 2. 监控和告警
- **实时监控**: 建立响应时间、错误率、工具成功率的实时监控
- **告警机制**: 设置阈值告警，及时发现性能问题
- **日志分析**: 定期分析日志，识别性能瓶颈

### 架构改进建议

#### 1. 系统架构优化
- **微服务化**: 将不同工具服务化，提高可扩展性
- **异步处理**: 对于耗时操作使用异步处理
- **资源隔离**: 避免单点故障影响整体性能

#### 2. 数据处理优化
- **批量处理**: 对相似请求进行批量处理
- **智能路由**: 根据查询类型智能选择最适合的工具
- **结果合并**: 优化多工具结果的合并策略

### 用户体验改进

#### 1. 响应时间改进
- **流式响应**: 对于长时间查询，提供流式响应
- **进度反馈**: 显示查询进度，提升用户体验
- **超时处理**: 合理设置超时时间，避免长时间等待

#### 2. 查询质量提升
- **查询优化**: 分析查询模式，优化查询策略
- **结果排序**: 改进结果排序算法，提高相关性
- **个性化**: 根据用户历史，提供个性化搜索体验
""".format(performance['avg_response_time'])
    
    return recommendations

def generate_full_report(results):
    """生成完整报告"""
    report = f"""# 🔍 代码检索基准测试 - 日志分析报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**分析周期**: 2025-08-21 12:27:24 - 2025-08-21 13:13:14

{generate_executive_summary(results)}

{analyze_performance_details(results)}

{analyze_tool_usage_details(results)}

{analyze_errors_and_issues(results)}

{generate_recommendations(results)}

## 📈 数据附录

### 原始数据统计
- **客户端日志**: log-2025-08-21-12-27-24.log
- **服务端日志**: app.log
- **分析结果**: log_analysis_results.json

### 分析方法
1. **日志解析**: 使用正则表达式和JSON解析提取结构化数据
2. **关联分析**: 通过traceId关联客户端和服务端日志
3. **统计分析**: 计算各项性能指标和分布统计
4. **趋势分析**: 识别性能趋势和异常模式

---
*本报告由自动化日志分析系统生成*
"""
    
    return report

def main():
    """主函数"""
    print("正在生成日志分析报告...")
    
    # 加载分析结果
    results = load_analysis_results()
    
    # 生成完整报告
    report = generate_full_report(results)
    
    # 保存报告
    with open('log_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ 报告已生成: log_analysis_report.md")
    
    # 打印摘要
    print("\n" + "="*60)
    print(generate_executive_summary(results))
    print("="*60)

if __name__ == "__main__":
    main()
