#!/usr/bin/env python3
"""
日志分析脚本 - 分析客户端和服务端日志文件
分析两份日志之间的traceId关联，提取性能指标和工具调用统计
"""

import re
import json
import pandas as pd
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import statistics

class LogAnalyzer:
    def __init__(self, client_log_path: str, server_log_path: str):
        self.client_log_path = client_log_path
        self.server_log_path = server_log_path
        self.client_logs = []
        self.server_logs = []
        self.trace_mapping = defaultdict(dict)

    def parse_timestamp(self, timestamp_str: str) -> datetime:
        """解析时间戳"""
        return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

    def parse_client_log(self) -> List[Dict]:
        """解析客户端日志文件"""
        print(f"解析客户端日志: {self.client_log_path}")

        with open(self.client_log_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取所有Duration记录
        duration_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?Duration: ([\d.]+)s'
        all_durations = []
        for match in re.finditer(duration_pattern, content):
            timestamp_str, duration = match.groups()
            timestamp = self.parse_timestamp(timestamp_str)
            all_durations.append({
                'timestamp': timestamp,
                'duration': float(duration)
            })

        # 提取Trace记录
        trace_pattern = r'Trace: ([a-f0-9]{32}), project: (\w+), question: (.+?)(?=\n|$)'
        trace_records = []
        for match in re.finditer(trace_pattern, content, re.MULTILINE):
            trace_id, project, question = match.groups()
            trace_records.append({
                'trace_id': trace_id,
                'project': project,
                'question': question.strip()
            })

        # 提取指标信息
        metrics_pattern = r'Trace: ([a-f0-9]{32}), project: (\w+), metrics: ({[^}]+})'
        metrics_data = {}
        for match in re.finditer(metrics_pattern, content, re.DOTALL):
            trace_id, project, metrics_str = match.groups()
            try:
                # 清理metrics字符串并解析JSON
                metrics_str = metrics_str.replace('\n', '').replace('  ', ' ')
                metrics = json.loads(metrics_str)
                metrics_data[trace_id] = metrics
            except json.JSONDecodeError as e:
                print(f"解析指标失败 {trace_id}: {e}")

        # 合并数据
        requests = []
        for trace_record in trace_records:
            trace_id = trace_record['trace_id']
            request_data = {
                'trace_id': trace_id,
                'project': trace_record['project'],
                'question': trace_record['question'],
                'metrics': metrics_data.get(trace_id)
            }
            requests.append(request_data)

        # 计算总任务数和总时间
        total_tasks = len(requests)
        total_time = sum(d['duration'] for d in all_durations)
        avg_time_per_task = total_time / total_tasks if total_tasks > 0 else 0

        print(f"总任务数: {total_tasks}")
        print(f"总执行时间: {total_time:.2f}秒")
        print(f"平均每任务时间: {avg_time_per_task:.2f}秒")

        # 为每个请求添加平均时间（因为是并发执行）
        for request in requests:
            request['avg_duration'] = avg_time_per_task

        # 保存所有持续时间记录用于分析
        self.all_durations = all_durations
        self.client_logs = requests
        return requests

    def parse_server_log(self) -> List[Dict]:
        """解析服务端日志文件"""
        print(f"解析服务端日志: {self.server_log_path}")

        with open(self.server_log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        server_logs = []
        current_trace = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 提取trace_id
            trace_match = re.search(r'trace_id=([a-f0-9]{32})', line)
            if trace_match:
                current_trace = trace_match.group(1)

            # 解析不同类型的日志
            timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            if timestamp_match and current_trace:
                timestamp = self.parse_timestamp(timestamp_match.group(1))

                log_entry = {
                    'timestamp': timestamp,
                    'trace_id': current_trace,
                    'line': line
                }

                # 识别不同类型的操作
                if 'Start DeepSearch' in line:
                    log_entry['type'] = 'search_start'
                elif 'DeepSearch Completed' in line:
                    log_entry['type'] = 'search_complete'
                elif 'Searching Tool:' in line:
                    # 提取搜索工具
                    tools_match = re.search(r'Searching Tool: \[([^\]]+)\]', line)
                    if tools_match:
                        tools_str = tools_match.group(1)
                        tools = re.findall(r"'(\w+)'", tools_str)
                        log_entry['type'] = 'tool_usage'
                        log_entry['tools'] = tools
                elif 'Found' in line and 'Code Snippets' in line:
                    # 提取找到的代码片段数量
                    snippets_match = re.search(r'Found (\d+) Code Snippets', line)
                    if snippets_match:
                        log_entry['type'] = 'snippets_found'
                        log_entry['snippets_count'] = int(snippets_match.group(1))
                elif 'LLM调用失败' in line:
                    log_entry['type'] = 'llm_error'
                elif 'WARNING' in line:
                    log_entry['type'] = 'warning'
                elif 'ERROR' in line:
                    log_entry['type'] = 'error'

                server_logs.append(log_entry)

        self.server_logs = server_logs
        return server_logs

    def correlate_logs(self):
        """关联客户端和服务端日志"""
        print("关联客户端和服务端日志...")

        for client_log in self.client_logs:
            trace_id = client_log['trace_id']
            self.trace_mapping[trace_id]['client'] = client_log
            self.trace_mapping[trace_id]['server'] = []

            # 找到对应的服务端日志
            for server_log in self.server_logs:
                if server_log['trace_id'] == trace_id:
                    self.trace_mapping[trace_id]['server'].append(server_log)

    def analyze_performance(self) -> Dict[str, Any]:
        """分析性能指标"""
        print("分析性能指标...")

        # 使用所有Duration记录进行分析
        all_durations = [d['duration'] for d in self.all_durations]
        total_tasks = len(self.client_logs)
        total_time = sum(all_durations)
        avg_time_per_task = total_time / total_tasks if total_tasks > 0 else 0

        # 按项目分组的指标
        project_metrics = defaultdict(list)
        for log in self.client_logs:
            if log.get('metrics'):
                project = log['project']
                metrics = log['metrics']
                project_metrics[project].append(metrics)

        # 计算平均指标
        avg_metrics_by_project = {}
        for project, metrics_list in project_metrics.items():
            if metrics_list:
                avg_metrics = {}
                # 获取所有指标键
                all_keys = set()
                for metrics in metrics_list:
                    all_keys.update(metrics.keys())

                # 计算每个指标的平均值
                for key in all_keys:
                    values = [m[key] for m in metrics_list if key in m]
                    if values:
                        avg_metrics[key] = statistics.mean(values)

                avg_metrics_by_project[project] = avg_metrics

        performance_stats = {
            'total_tasks': total_tasks,
            'total_execution_time': total_time,
            'avg_time_per_task': avg_time_per_task,
            'min_duration': min(all_durations) if all_durations else 0,
            'max_duration': max(all_durations) if all_durations else 0,
            'median_duration': statistics.median(all_durations) if all_durations else 0,
            'all_durations': all_durations,
            'project_metrics': avg_metrics_by_project,
            # 保持兼容性的字段
            'total_requests': len(self.client_logs),
            'avg_response_time': avg_time_per_task,
            'min_response_time': min(all_durations) if all_durations else 0,
            'max_response_time': max(all_durations) if all_durations else 0,
            'median_response_time': statistics.median(all_durations) if all_durations else 0,
            'response_times': all_durations
        }

        return performance_stats

    def analyze_tool_usage(self) -> Dict[str, Any]:
        """分析工具调用统计"""
        print("分析工具调用统计...")

        tool_counter = Counter()
        tool_usage_by_trace = defaultdict(list)

        for server_log in self.server_logs:
            if server_log.get('type') == 'tool_usage' and server_log.get('tools'):
                trace_id = server_log['trace_id']
                tools = server_log['tools']

                for tool in tools:
                    tool_counter[tool] += 1
                    tool_usage_by_trace[trace_id].extend(tools)

        return {
            'tool_distribution': dict(tool_counter),
            'tool_usage_by_trace': dict(tool_usage_by_trace),
            'total_tool_calls': sum(tool_counter.values())
        }

    def analyze_errors_and_warnings(self) -> Dict[str, Any]:
        """分析错误和警告"""
        print("分析错误和警告...")

        errors = []
        warnings = []

        for server_log in self.server_logs:
            if server_log.get('type') == 'error':
                errors.append(server_log)
            elif server_log.get('type') == 'warning':
                warnings.append(server_log)
            elif server_log.get('type') == 'llm_error':
                errors.append(server_log)

        return {
            'total_errors': len(errors),
            'total_warnings': len(warnings),
            'errors': errors,
            'warnings': warnings
        }

    def run_analysis(self) -> Dict[str, Any]:
        """运行完整分析"""
        print("开始日志分析...")

        # 解析日志
        self.parse_client_log()
        self.parse_server_log()

        # 关联日志
        self.correlate_logs()

        # 执行各项分析
        performance = self.analyze_performance()
        tool_usage = self.analyze_tool_usage()
        errors_warnings = self.analyze_errors_and_warnings()

        return {
            'performance': performance,
            'tool_usage': tool_usage,
            'errors_warnings': errors_warnings,
            'trace_mapping': dict(self.trace_mapping)
        }

def main():
    # 分析日志
    analyzer = LogAnalyzer('log-2025-08-21-12-27-24.log', 'app.log')
    results = analyzer.run_analysis()

    # 保存结果到JSON文件
    with open('log_analysis_results.json', 'w', encoding='utf-8') as f:
        # 转换datetime对象为字符串以便JSON序列化
        def datetime_converter(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

        json.dump(results, f, ensure_ascii=False, indent=2, default=datetime_converter)

    print("分析完成，结果已保存到 log_analysis_results.json")
    return results

if __name__ == "__main__":
    main()