#!/usr/bin/env python3
"""
Log Analysis Script for Code Retrieval Benchmarks
Analyzes concurrent task logs grouped by traceId
"""

import re
import json
from datetime import datetime
from collections import defaultdict, Counter
import statistics

def parse_log_file(log_file_path):
    """Parse the log file and extract task data by traceId"""
    tasks = defaultdict(dict)
    
    with open(log_file_path, 'r') as f:
        for line in f:
            # Skip empty lines
            if not line.strip():
                continue
            
            # Parse timestamp
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            if not timestamp_match:
                continue
            
            timestamp_str = timestamp_match.group(1)
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            
            # Extract request start events
            if '🚀 [REQUEST START]' in line:
                tasks['requests'].setdefault('starts', []).append({
                    'timestamp': timestamp,
                    'line': line.strip()
                })
            
            # Extract request end events
            elif '✅ [REQUEST END]' in line:
                duration_match = re.search(r'Duration: ([\d.]+)s', line)
                duration = float(duration_match.group(1)) if duration_match else 0
                
                tasks['requests'].setdefault('ends', []).append({
                    'timestamp': timestamp,
                    'duration': duration,
                    'line': line.strip()
                })
            
            # Extract trace information
            elif 'Trace:' in line and 'project:' in line and 'question:' in line:
                trace_match = re.search(r'Trace: ([a-f0-9]+), project: ([^,]+), question: (.+)', line)
                if trace_match:
                    trace_id, project, question = trace_match.groups()
                    
                    if trace_id not in tasks:
                        tasks[trace_id] = {}
                    
                    tasks[trace_id].update({
                        'trace_id': trace_id,
                        'project': project,
                        'question': question.strip(),
                        'timestamp': timestamp
                    })
            
            # Extract metrics
            elif 'Trace:' in line and 'metrics:' in line:
                trace_match = re.search(r'Trace: ([a-f0-9]+).*metrics: {', line)
                if trace_match:
                    trace_id = trace_match.group(1)
                    
                    # Find the complete metrics block
                    metrics_start = line.find('{')
                    if metrics_start != -1:
                        # This is simplified - in real parsing, we'd need to handle multi-line JSON
                        pass
            
            # Extract retrieved docs
            elif 'Trace:' in line and 'retrieved_docs:' in line:
                trace_match = re.search(r'Trace: ([a-f0-9]+).*retrieved_docs:', line)
                if trace_match:
                    trace_id = trace_match.group(1)
                    if trace_id in tasks:
                        tasks[trace_id]['has_retrieved_docs'] = True
            
            # Extract relevant docs
            elif 'Trace:' in line and 'relevant_docs:' in line:
                trace_match = re.search(r'Trace: ([a-f0-9]+).*relevant_docs:', line)
                if trace_match:
                    trace_id = trace_match.group(1)
                    if trace_id in tasks:
                        tasks[trace_id]['has_relevant_docs'] = True
    
    return tasks

def extract_metrics_from_log(log_file_path):
    """Extract detailed metrics for each trace"""
    metrics_data = {}
    
    with open(log_file_path, 'r') as f:
        lines = f.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Look for trace metrics
        if 'Trace:' in line and 'metrics: {' in line:
            trace_match = re.search(r'Trace: ([a-f0-9]+)', line)
            if trace_match:
                trace_id = trace_match.group(1)
                
                # Extract the metrics JSON (spans multiple lines)
                metrics_text = ""
                j = i
                brace_count = 0
                found_start = False
                
                while j < len(lines):
                    current_line = lines[j]
                    if '{' in current_line and not found_start:
                        found_start = True
                        start_pos = current_line.find('{')
                        metrics_text += current_line[start_pos:]
                        brace_count += current_line[start_pos:].count('{')
                        brace_count -= current_line[start_pos:].count('}')
                    elif found_start:
                        # Remove line number prefix if exists
                        clean_line = re.sub(r'^\s*\d+→', '', current_line)
                        metrics_text += clean_line
                        brace_count += clean_line.count('{')
                        brace_count -= clean_line.count('}')
                    
                    if found_start and brace_count == 0:
                        break
                    j += 1
                
                # Parse the metrics JSON
                try:
                    metrics = json.loads(metrics_text)
                    metrics_data[trace_id] = metrics
                except json.JSONDecodeError:
                    print(f"Failed to parse metrics for trace {trace_id}")
        
        i += 1
    
    return metrics_data

def analyze_execution_times(log_file_path):
    """Analyze execution times from request start/end pairs"""
    execution_times = []
    
    with open(log_file_path, 'r') as f:
        lines = f.readlines()
    
    # Extract all request start times
    start_times = []
    end_data = []
    
    for line in lines:
        if '🚀 [REQUEST START]' in line:
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            if timestamp_match:
                timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                start_times.append(timestamp)
        
        elif '✅ [REQUEST END]' in line:
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            duration_match = re.search(r'Duration: ([\d.]+)s', line)
            
            if timestamp_match and duration_match:
                timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                duration = float(duration_match.group(1))
                end_data.append({'timestamp': timestamp, 'duration': duration})
    
    return {
        'start_times': start_times,
        'end_data': end_data,
        'total_requests': len(start_times),
        'completed_requests': len(end_data)
    }

def analyze_projects_and_performance(log_file_path):
    """Analyze project-specific performance"""
    project_data = defaultdict(list)
    
    with open(log_file_path, 'r') as f:
        content = f.read()
    
    # Extract project metrics
    project_pattern = r'Project (\w+) metrics: ({[^}]+})'
    matches = re.findall(project_pattern, content)
    
    for project, metrics_str in matches:
        try:
            # Clean up the metrics string
            metrics_str = metrics_str.replace("'", '"')
            metrics = json.loads(metrics_str)
            project_data[project] = metrics
        except json.JSONDecodeError:
            continue
    
    return project_data

def generate_analysis_report(log_file_path):
    """Generate comprehensive analysis report"""
    
    print("🔍 Code Retrieval Benchmarks Log Analysis Report")
    print("=" * 60)
    
    # Parse basic task data
    tasks = parse_log_file(log_file_path)
    
    # Extract metrics
    metrics_data = extract_metrics_from_log(log_file_path)
    
    # Analyze execution times
    timing_data = analyze_execution_times(log_file_path)
    
    # Analyze project performance
    project_data = analyze_projects_and_performance(log_file_path)
    
    # Summary Statistics
    print("\n📊 SUMMARY STATISTICS")
    print("-" * 30)
    
    # Filter out non-trace entries
    trace_tasks = {k: v for k, v in tasks.items() if k != 'requests' and isinstance(v, dict) and 'trace_id' in v}
    
    print(f"Total Tasks Analyzed: {len(trace_tasks)}")
    print(f"Total HTTP Requests: {timing_data['total_requests']}")
    print(f"Completed Requests: {timing_data['completed_requests']}")
    print(f"Projects Evaluated: {len(project_data)}")
    
    # Execution Time Analysis
    print("\n⏱️  EXECUTION TIME ANALYSIS")
    print("-" * 30)
    
    if timing_data['end_data']:
        durations = [item['duration'] for item in timing_data['end_data']]
        
        print(f"Average Execution Time: {statistics.mean(durations):.2f} seconds")
        print(f"Median Execution Time: {statistics.median(durations):.2f} seconds")
        print(f"Min Execution Time: {min(durations):.2f} seconds")
        print(f"Max Execution Time: {max(durations):.2f} seconds")
        print(f"Standard Deviation: {statistics.stdev(durations) if len(durations) > 1 else 0:.2f} seconds")
        
        # Time distribution
        quick_tasks = sum(1 for d in durations if d < 30)
        medium_tasks = sum(1 for d in durations if 30 <= d < 60)
        slow_tasks = sum(1 for d in durations if d >= 60)
        
        print(f"\nExecution Time Distribution:")
        print(f"  Fast (< 30s): {quick_tasks} ({quick_tasks/len(durations)*100:.1f}%)")
        print(f"  Medium (30-60s): {medium_tasks} ({medium_tasks/len(durations)*100:.1f}%)")
        print(f"  Slow (≥ 60s): {slow_tasks} ({slow_tasks/len(durations)*100:.1f}%)")
    
    # Tool Usage Analysis
    print("\n🛠️  TOOL USAGE ANALYSIS")
    print("-" * 30)
    
    # Based on the log structure, the main "tool" being used is HTTP search requests
    print("Primary Tool: HTTP POST /api/v1/search")
    print(f"Total Search Requests: {timing_data['total_requests']}")
    print(f"Success Rate: {timing_data['completed_requests']/timing_data['total_requests']*100:.1f}%" if timing_data['total_requests'] > 0 else "N/A")
    
    # Connection issues analysis
    with open(log_file_path, 'r') as f:
        log_content = f.read()
    
    retry_count = log_content.count('Retrying')
    connection_reset_count = log_content.count('Connection reset by peer')
    
    print(f"Connection Retries: {retry_count}")
    print(f"Connection Reset Errors: {connection_reset_count}")
    print(f"Error Rate: {(retry_count + connection_reset_count)/timing_data['total_requests']*100:.1f}%" if timing_data['total_requests'] > 0 else "N/A")
    
    # Performance Metrics Analysis
    print("\n📈 PERFORMANCE METRICS ANALYSIS")
    print("-" * 30)
    
    if metrics_data:
        all_p5 = []
        all_recall5 = []
        all_ndcg5 = []
        all_map5 = []
        
        for trace_id, metrics in metrics_data.items():
            if 'P@5' in metrics:
                all_p5.append(metrics['P@5'])
            if 'Recall@5' in metrics:
                all_recall5.append(metrics['Recall@5'])
            if 'NDCG@5' in metrics:
                all_ndcg5.append(metrics['NDCG@5'])
            if 'MAP@5' in metrics:
                all_map5.append(metrics['MAP@5'])
        
        if all_p5:
            print(f"Average P@5: {statistics.mean(all_p5):.4f}")
            print(f"Average Recall@5: {statistics.mean(all_recall5):.4f}")
            print(f"Average NDCG@5: {statistics.mean(all_ndcg5):.4f}")
            print(f"Average MAP@5: {statistics.mean(all_map5):.4f}")
    
    # Project-specific Analysis
    print("\n🏗️  PROJECT ANALYSIS")
    print("-" * 30)
    
    for project, metrics in project_data.items():
        print(f"\n{project}:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.4f}")
    
    # Task Categories Analysis
    print("\n📝 TASK CATEGORIES ANALYSIS")
    print("-" * 30)
    
    question_keywords = defaultdict(int)
    for trace_id, task in trace_tasks.items():
        if 'question' in task:
            question = task['question'].lower()
            
            # Categorize by keywords
            if any(word in question for word in ['性能', 'performance', '优化', 'optimize']):
                question_keywords['Performance'] += 1
            elif any(word in question for word in ['安全', 'security', '认证', 'auth', '漏洞']):
                question_keywords['Security'] += 1
            elif any(word in question for word in ['文档', 'document', '指南', 'guide']):
                question_keywords['Documentation'] += 1
            elif any(word in question for word in ['测试', 'test', '验证']):
                question_keywords['Testing'] += 1
            elif any(word in question for word in ['分析', 'analyze', '评审', 'review']):
                question_keywords['Analysis'] += 1
            elif any(word in question for word in ['实现', 'implement', '编写', 'create']):
                question_keywords['Implementation'] += 1
            else:
                question_keywords['Other'] += 1
    
    for category, count in question_keywords.items():
        percentage = count / len(trace_tasks) * 100 if trace_tasks else 0
        print(f"{category}: {count} tasks ({percentage:.1f}%)")
    
    print(f"\n✅ Analysis Complete - Processed {len(trace_tasks)} tasks")

if __name__ == "__main__":
    log_file_path = "/Users/<USER>/01-Projects/ClaudePlay/log-2025-08-21-10-48-27.log"
    generate_analysis_report(log_file_path)