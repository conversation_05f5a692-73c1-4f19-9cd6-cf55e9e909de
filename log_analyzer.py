#!/usr/bin/env python3
"""
日志分析脚本 - 分析客户端和服务端日志文件
分析两份日志之间的traceId关联，提取性能指标和工具调用统计
"""

import re
import json
import pandas as pd
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import statistics

class LogAnalyzer:
    def __init__(self, client_log_path: str, server_log_path: str):
        self.client_log_path = client_log_path
        self.server_log_path = server_log_path
        self.client_logs = []
        self.server_logs = []
        self.trace_mapping = defaultdict(dict)

    def parse_timestamp(self, timestamp_str: str) -> datetime:
        """解析时间戳"""
        return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

    def parse_client_log(self) -> List[Dict]:
        """解析客户端日志文件"""
        print(f"解析客户端日志: {self.client_log_path}")

        with open(self.client_log_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取请求记录
        request_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?Trace: ([a-f0-9]{32}), project: (\w+), question: (.+?)(?=\n\d{4}-\d{2}-\d{2}|\nTrace:|\Z)'
        duration_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?Duration: ([\d.]+)s'
        metrics_pattern = r'Trace: ([a-f0-9]{32}), project: (\w+), metrics: ({[^}]+})'

        # 提取持续时间
        durations = {}
        for match in re.finditer(duration_pattern, content, re.DOTALL):
            timestamp_str, duration = match.groups()
            timestamp = self.parse_timestamp(timestamp_str)
            durations[timestamp] = float(duration)

        # 提取请求信息
        requests = []
        for match in re.finditer(request_pattern, content, re.DOTALL):
            timestamp_str, trace_id, project, question = match.groups()
            timestamp = self.parse_timestamp(timestamp_str)

            # 查找对应的持续时间
            duration = None
            for ts, dur in durations.items():
                if abs((ts - timestamp).total_seconds()) < 5:  # 5秒内的匹配
                    duration = dur
                    break

            requests.append({
                'timestamp': timestamp,
                'trace_id': trace_id,
                'project': project,
                'question': question.strip(),
                'duration': duration
            })

        # 提取指标信息
        for match in re.finditer(metrics_pattern, content, re.DOTALL):
            trace_id, project, metrics_str = match.groups()
            try:
                # 清理metrics字符串并解析JSON
                metrics_str = metrics_str.replace('\n', '').replace('  ', ' ')
                metrics = json.loads(metrics_str)

                # 找到对应的请求并添加指标
                for req in requests:
                    if req['trace_id'] == trace_id:
                        req['metrics'] = metrics
                        break
            except json.JSONDecodeError as e:
                print(f"解析指标失败 {trace_id}: {e}")

        self.client_logs = requests
        return requests

    def parse_server_log(self) -> List[Dict]:
        """解析服务端日志文件"""
        print(f"解析服务端日志: {self.server_log_path}")

        with open(self.server_log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        server_logs = []
        current_trace = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 提取trace_id
            trace_match = re.search(r'trace_id=([a-f0-9]{32})', line)
            if trace_match:
                current_trace = trace_match.group(1)

            # 解析不同类型的日志
            timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            if timestamp_match and current_trace:
                timestamp = self.parse_timestamp(timestamp_match.group(1))

                log_entry = {
                    'timestamp': timestamp,
                    'trace_id': current_trace,
                    'line': line
                }

                # 识别不同类型的操作
                if 'Start DeepSearch' in line:
                    log_entry['type'] = 'search_start'
                elif 'DeepSearch Completed' in line:
                    log_entry['type'] = 'search_complete'
                elif 'Searching Tool:' in line:
                    # 提取搜索工具
                    tools_match = re.search(r'Searching Tool: \[([^\]]+)\]', line)
                    if tools_match:
                        tools_str = tools_match.group(1)
                        tools = re.findall(r"'(\w+)'", tools_str)
                        log_entry['type'] = 'tool_usage'
                        log_entry['tools'] = tools
                elif 'Found' in line and 'Code Snippets' in line:
                    # 提取找到的代码片段数量
                    snippets_match = re.search(r'Found (\d+) Code Snippets', line)
                    if snippets_match:
                        log_entry['type'] = 'snippets_found'
                        log_entry['snippets_count'] = int(snippets_match.group(1))
                elif 'LLM调用失败' in line:
                    log_entry['type'] = 'llm_error'
                elif 'WARNING' in line:
                    log_entry['type'] = 'warning'
                elif 'ERROR' in line:
                    log_entry['type'] = 'error'

                server_logs.append(log_entry)

        self.server_logs = server_logs
        return server_logs

    def correlate_logs(self):
        """关联客户端和服务端日志"""
        print("关联客户端和服务端日志...")

        for client_log in self.client_logs:
            trace_id = client_log['trace_id']
            self.trace_mapping[trace_id]['client'] = client_log
            self.trace_mapping[trace_id]['server'] = []

            # 找到对应的服务端日志
            for server_log in self.server_logs:
                if server_log['trace_id'] == trace_id:
                    self.trace_mapping[trace_id]['server'].append(server_log)

    def analyze_performance(self) -> Dict[str, Any]:
        """分析性能指标"""
        print("分析性能指标...")

        # 收集所有有效的持续时间
        durations = [log['duration'] for log in self.client_logs if log.get('duration')]

        # 按项目分组的指标
        project_metrics = defaultdict(list)
        for log in self.client_logs:
            if log.get('metrics'):
                project = log['project']
                metrics = log['metrics']
                project_metrics[project].append(metrics)

        # 计算平均指标
        avg_metrics_by_project = {}
        for project, metrics_list in project_metrics.items():
            if metrics_list:
                avg_metrics = {}
                # 获取所有指标键
                all_keys = set()
                for metrics in metrics_list:
                    all_keys.update(metrics.keys())

                # 计算每个指标的平均值
                for key in all_keys:
                    values = [m[key] for m in metrics_list if key in m]
                    if values:
                        avg_metrics[key] = statistics.mean(values)

                avg_metrics_by_project[project] = avg_metrics

        performance_stats = {
            'total_requests': len(self.client_logs),
            'avg_response_time': statistics.mean(durations) if durations else 0,
            'min_response_time': min(durations) if durations else 0,
            'max_response_time': max(durations) if durations else 0,
            'median_response_time': statistics.median(durations) if durations else 0,
            'response_times': durations,
            'project_metrics': avg_metrics_by_project
        }

        return performance_stats

    def analyze_tool_usage(self) -> Dict[str, Any]:
        """分析工具调用统计"""
        print("分析工具调用统计...")

        tool_counter = Counter()
        tool_usage_by_trace = defaultdict(list)

        for server_log in self.server_logs:
            if server_log.get('type') == 'tool_usage' and server_log.get('tools'):
                trace_id = server_log['trace_id']
                tools = server_log['tools']

                for tool in tools:
                    tool_counter[tool] += 1
                    tool_usage_by_trace[trace_id].extend(tools)

        return {
            'tool_distribution': dict(tool_counter),
            'tool_usage_by_trace': dict(tool_usage_by_trace),
            'total_tool_calls': sum(tool_counter.values())
        }

    def analyze_errors_and_warnings(self) -> Dict[str, Any]:
        """分析错误和警告"""
        print("分析错误和警告...")

        errors = []
        warnings = []

        for server_log in self.server_logs:
            if server_log.get('type') == 'error':
                errors.append(server_log)
            elif server_log.get('type') == 'warning':
                warnings.append(server_log)
            elif server_log.get('type') == 'llm_error':
                errors.append(server_log)

        return {
            'total_errors': len(errors),
            'total_warnings': len(warnings),
            'errors': errors,
            'warnings': warnings
        }

    def run_analysis(self) -> Dict[str, Any]:
        """运行完整分析"""
        print("开始日志分析...")

        # 解析日志
        self.parse_client_log()
        self.parse_server_log()

        # 关联日志
        self.correlate_logs()

        # 执行各项分析
        performance = self.analyze_performance()
        tool_usage = self.analyze_tool_usage()
        errors_warnings = self.analyze_errors_and_warnings()

        return {
            'performance': performance,
            'tool_usage': tool_usage,
            'errors_warnings': errors_warnings,
            'trace_mapping': dict(self.trace_mapping)
        }

def main():
    # 分析日志
    analyzer = LogAnalyzer('log-2025-08-21-12-27-24.log', 'app.log')
    results = analyzer.run_analysis()

    # 保存结果到JSON文件
    with open('log_analysis_results.json', 'w', encoding='utf-8') as f:
        # 转换datetime对象为字符串以便JSON序列化
        def datetime_converter(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

        json.dump(results, f, ensure_ascii=False, indent=2, default=datetime_converter)

    print("分析完成，结果已保存到 log_analysis_results.json")
    return results

if __name__ == "__main__":
    main()